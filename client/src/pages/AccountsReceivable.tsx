/**
 * Accounts Receivable Module
 *
 * This page handles the Accounts Receivable workflow in the application.
 * It provides a Kanban-style interface for managing incoming invoices and payments:
 * - Importing/creating invoices
 * - Tracking invoice status (Open, Overdue, Paid)
 * - Receiving incoming payments
 * - Linking payments to invoices
 * - Generating reconciliation documents
 *
 * The workflow follows these stages:
 * 1. Invoices are created or imported (visible in first column)
 * 2. Payments are received from customers (visible in second column)
 * 3. Payments are linked to invoices to track which invoices have been paid
 * 4. Reconciliation documents can be generated for completed payments
 */

import { useState } from "react";
import { Button } from "@/components/ui/button";
import KanbanBoard, { KanbanColumn } from "@/components/KanbanBoard";
import InvoiceCard from "@/components/InvoiceCard";
import ReceivedPaymentCard from "@/components/ReceivedPaymentCard";
import DetailPanel from "@/components/DetailPanel";
import ProcessingOverlay from "@/components/ProcessingOverlay";
import UploadModal from "@/components/UploadModal";
import ImportPaymentModal from "@/components/ImportPaymentModal";
import { useInvoices } from "@/hooks/useInvoices";
import { useReceivedPayments } from "@/hooks/useReceivedPayments";
import { useToast } from "@/hooks/use-toast";
import { useDemoMode } from "@/hooks/useDemoMode";
import { useQueryClient } from "@tanstack/react-query";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

/**
 * Accounts Receivable Component
 *
 * This component implements the main Accounts Receivable workflow interface.
 * It manages invoices, incoming payments, and reconciliation generation.
 * The component uses a Kanban-style board layout with two columns:
 * 1. Invoices column - Shows all invoices with their statuses
 * 2. Received Payments column - Shows incoming payments (linked and unlinked)
 *
 * Features:
 * - Import invoice files in standardized formats
 * - Create invoices manually
 * - Receive incoming payments
 * - Link payments to invoices
 * - Generate reconciliation documents in multiple formats
 * - Detail panels that slide out when an item is selected
 *
 * @returns React component for Accounts Receivable workflow
 */
const AccountsReceivable = () => {
  // Hooks for notifications and data management
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { isDemoMode } = useDemoMode();

  // Fetch invoices data from API
  const {
    allInvoices,
    isLoading: invoicesLoading,
    error: invoicesError,
  } = useInvoices();

  // Fetch received payments data from API
  const {
    receivedPayments,
    isLoading: paymentsLoading,
    error: paymentsError,
    createReceivedPaymentMutation,
    getReceivedPaymentById,
  } = useReceivedPayments();

  /**
   * UI State Management
   * The following state variables control the user interface elements:
   * - Detail panel display and content
   * - Processing overlay for blockchain operations
   * - Modal dialogs for importing files and simulating payments
   */

  // Detail panel state - tracks which item is selected and displayed
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);                // ID of selected invoice or payment
  const [selectedItemType, setSelectedItemType] = useState<"invoice" | "receivedPayment" | null>(null); // Type of selected item
  const [isPanelOpen, setIsPanelOpen] = useState(false);                                   // Whether detail panel is visible

  // Processing overlay state - used during blockchain operations
  const [isProcessing, setIsProcessing] = useState(false);                                 // Whether processing overlay is visible
  const [processingTitle, setProcessingTitle] = useState("");                              // Title for processing overlay
  const [processingMessage, setProcessingMessage] = useState("");                          // Message for processing overlay

  // Modal dialogs state
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);                       // Invoice file upload modal
  const [isSimulateModalOpen, setIsSimulateModalOpen] = useState(false);                   // Payment simulation modal
  const [showImportPay, setShowImportPay] = useState(false);                               // Import payment JSON modal

  /**
   * Form State
   * This state manages the input fields for the payment simulation form
   */
  const [simulatedPayment, setSimulatedPayment] = useState({
    sender: "",                    // Customer/client sending the payment
    recipient: "Your Company",     // Default recipient (your company)
    amount: 0,                     // Payment amount
    reference: "",                 // Payment reference number
  });

  /**
   * Derived State
   * These variables are computed from other state variables
   */

  // Combined loading state - true if either invoices or payments are loading
  const isLoading = invoicesLoading || paymentsLoading;

  // Combined error state - contains first error encountered from either data source
  const error = invoicesError || paymentsError;

  // Data comes directly from the hooks without additional filtering

  /**
   * Selected Items
   * These variables find the full data objects for the selected items
   * based on their IDs and types
   */
  const selectedInvoice = selectedItemType === "invoice" && selectedItemId
    ? allInvoices.find(i => i.id === selectedItemId)
    : null;

  const selectedPayment = selectedItemType === "receivedPayment" && selectedItemId
    ? receivedPayments.find(p => p.id === selectedItemId)
    : null;

  /**
   * Event Handlers
   * The following functions handle user interactions with the interface
   */

  /**
   * Handle invoice card click
   *
   * Opens or closes the detail panel for an invoice when clicked
   * - If clicking the same invoice while panel is open: close the panel
   * - If clicking a different invoice or first click: open panel with invoice details
   *
   * @param id - ID of the clicked invoice
   */
  const handleInvoiceClick = (id: number) => {
    if (selectedItemId === id && selectedItemType === "invoice" && isPanelOpen) {
      // If clicking the same invoice, close the panel with animation
      setIsPanelOpen(false);
      setTimeout(() => {
        setSelectedItemId(null);
        setSelectedItemType(null);
      }, 600); // Match with animation duration in CSS
    } else {
      // Direct switching between any panels with one click
      setSelectedItemId(id);
      setSelectedItemType("invoice");
      // Make sure panel is open
      if (!isPanelOpen) {
        setIsPanelOpen(true);
      }
    }
  };

  /**
   * Handle payment card click
   *
   * Opens or closes the detail panel for a received payment when clicked
   * - If clicking the same payment while panel is open: close the panel
   * - If clicking a different payment or first click: open panel with payment details
   *
   * @param id - ID of the clicked payment
   */
  const handlePaymentClick = (id: number) => {
    if (selectedItemId === id && selectedItemType === "receivedPayment" && isPanelOpen) {
      // If clicking the same payment, close the panel with animation
      setIsPanelOpen(false);
      setTimeout(() => {
        setSelectedItemId(null);
        setSelectedItemType(null);
      }, 600); // Match with animation duration in CSS
    } else {
      // Direct switching between any panels with one click
      setSelectedItemId(id);
      setSelectedItemType("receivedPayment");
      // Make sure panel is open
      if (!isPanelOpen) {
        setIsPanelOpen(true);
      }
    }
  };

  /**
   * Handle closing the detail panel
   *
   * Initiates the slide-out animation and clears selection state
   * after animation completes
   */
  const handleClosePanel = () => {
    setIsPanelOpen(false);
    setTimeout(() => {
      setSelectedItemId(null);
      setSelectedItemType(null);
    }, 600); // Wait for animation to complete - match with animation duration in CSS
  };

  /**
   * Handle opening the invoice upload modal
   *
   * Shows the modal dialog for importing invoice files
   */
  const handleOpenUploadModal = () => {
    setIsUploadModalOpen(true);
  };

  /**
   * Handle opening the payment simulation modal
   *
   * Shows the modal dialog for creating a simulated incoming payment
   * This is used for testing and demonstration purposes
   */
  const handleOpenSimulateModal = () => {
    setIsSimulateModalOpen(true);
  };

  /**
   * Handle changes to the simulated payment form inputs
   *
   * Updates the state with form field values as they are changed
   * Performs input type conversion for the amount field
   *
   * @param e - Input change event
   */
  const handleSimulateInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSimulatedPayment(prev => ({
      ...prev,
      // Convert amount to number, other fields remain as strings
      [name]: name === "amount" ? parseFloat(value) || 0 : value,
    }));
  };

  /**
   * Handle submission of the simulated payment form
   *
   * Validates input data, then creates a new received payment record
   * Shows processing overlay during API call
   * Resets form after successful submission
   */
  const handleSimulateSubmit = async () => {
    // Form validation
    if (!simulatedPayment.sender || !simulatedPayment.reference || simulatedPayment.amount <= 0) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields with valid values.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Show processing overlay
      setIsProcessing(true);
      setProcessingTitle("Processing Payment");
      setProcessingMessage("Simulating incoming payment...");

      // Call API to create the payment
      await createReceivedPaymentMutation.mutateAsync(simulatedPayment);

      // Reset form and close modal on success
      setIsSimulateModalOpen(false);
      setSimulatedPayment({
        sender: "",
        recipient: "Your Company",
        amount: 0,
        reference: "",
      });

      // Show success notification
      toast({
        title: "Payment Received",
        description: "Incoming payment has been processed successfully.",
      });
    } catch (error) {
      // Show error notification
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to process payment.",
        variant: "destructive",
      });
    } finally {
      // Hide processing overlay
      setIsProcessing(false);
    }
  };

  /**
   * Handle successful file upload completion
   *
   * Displays a success toast notification
   * Note: The actual data refresh is handled by the UploadModal component
   * which invalidates the query cache to trigger a refresh
   */
  const handleUploadSuccess = () => {
    toast({
      title: "Success",
      description: "Invoice file has been processed successfully.",
    });
  };

  if (error) {
    return (
      <div className="container mx-auto p-4 text-center py-10">
        <h3 className="text-xl font-semibold text-red-500 mb-2">Error Loading Data</h3>
        <p className="text-gray-600">
          {error instanceof Error ? error.message : "Failed to load data. Please try again."}
        </p>
        <Button
          className="mt-4"
          onClick={() => window.location.reload()}
        >
          Retry
        </Button>
      </div>
    );
  }

  return (
    <main className="container mx-auto p-4">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-3">Accounts Receivable Pipeline</h2>
        {!isDemoMode && (
          <div className="flex space-x-2">
            <Button
              className="bg-primary hover:bg-blue-600 text-white flex items-center"
              onClick={handleOpenUploadModal}
              size="sm"
            >
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                ></path>
              </svg>
              Import Invoice File
            </Button>
            <Button
              variant="secondary"
              onClick={() => setShowImportPay(true)}
              size="sm"
            >
              Import Payment
            </Button>
          </div>
        )}
        {isDemoMode && (
          <div className="text-sm text-orange-600 bg-orange-50 px-3 py-2 rounded-md border border-orange-200">
            🎭 Demo Mode: Import buttons hidden - incoming payments are generated automatically
          </div>
        )}
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center h-[calc(100vh-200px)]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : (
        <KanbanBoard
          activeDetailPanel={
            isPanelOpen && selectedItemId && selectedItemType
              ? {
                  columnId:
                    selectedItemType === "invoice"
                      ? "invoices"
                      : "received-payments",
                  content: (
                    <DetailPanel
                      isOpen={isPanelOpen}
                      onClose={handleClosePanel}
                      item={selectedItemType === "invoice" ? selectedInvoice : selectedPayment}
                      itemType={selectedItemType}
                      onRefresh={async () => {
                        /**
                         * Data Refresh Handler for Detail Panel
                         *
                         * This callback refreshes data when user performs an action in the detail panel:
                         * 1. Invalidates both data queries to ensure all lists are updated
                         * 2. For received payments, also forces an immediate refresh of the selected item
                         *    to ensure we have the most current data for that specific item
                         *
                         * Note: We use separate invalidation calls to ensure both queries are refreshed
                         * independently, as they might have different refresh policies
                         */

                        // Invalidate both API endpoints to refresh all data
                        queryClient.invalidateQueries({ queryKey: ["/api/received-payments"] });
                        queryClient.invalidateQueries({ queryKey: ["/api/invoices"] });

                        // For payments, fetch the specific item directly to ensure
                        // we have the latest status and relationship data
                        if (selectedItemType === "receivedPayment" && selectedItemId) {
                          await getReceivedPaymentById(selectedItemId);
                        }
                      }}
                      inlineMode={true}
                    />
                  )
                }
              : null
          }
        >
          {/* All Invoices Column */}
          <KanbanColumn title="Invoices" count={allInvoices.length} columnId="invoices">
            {allInvoices.length === 0 ? (
              <div className="text-center py-6 text-gray-500 text-sm">
                No invoices
              </div>
            ) : (
              allInvoices.map((invoice) => (
                <div key={`invoice-wrapper-${invoice.id}`}>
                  <InvoiceCard
                    key={invoice.id}
                    id={invoice.id}
                    reference={invoice.reference}
                    status={invoice.status}
                    customer={invoice.customer}
                    amount={invoice.amount}
                    dueDate={invoice.due_date}
                    onClick={handleInvoiceClick}
                  />
                </div>
              ))
            )}
          </KanbanColumn>

          {/* All Payments Column */}
          <KanbanColumn title="Received Payments" count={receivedPayments.length} columnId="received-payments">
            {receivedPayments.length === 0 ? (
              <div className="text-center py-6 text-gray-500 text-sm">
                No payments
              </div>
            ) : (
              receivedPayments.map((payment) => (
                <div key={`payment-wrapper-${payment.id}`}>
                  <ReceivedPaymentCard
                    key={payment.id}
                    id={payment.id}
                    reference={payment.reference}
                    status={payment.status}
                    sender={payment.sender}
                    amount={payment.amount}
                    createdAt={payment.created_at}
                    invoiceId={payment.invoice_id}
                    onClick={handlePaymentClick}
                  />
                </div>
              ))
            )}
          </KanbanColumn>
        </KanbanBoard>
      )}

      {/* We've moved the detail panel to be inline in the KanbanBoard */}

      {/* Processing Overlay */}
      <ProcessingOverlay
        isOpen={isProcessing}
        title={processingTitle}
        message={processingMessage}
      />

      {/* Upload Modal */}
      <UploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onSuccess={handleUploadSuccess}
        type="invoice"
      />

      {/* Simulate Received Payment Modal */}
      <Dialog open={isSimulateModalOpen} onOpenChange={setIsSimulateModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Simulate Received Payment</DialogTitle>
            <DialogDescription>
              Create a simulated incoming payment for testing purposes.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="sender" className="text-right">
                Sender
              </Label>
              <Input
                id="sender"
                name="sender"
                value={simulatedPayment.sender}
                onChange={handleSimulateInputChange}
                className="col-span-3"
                placeholder="Company Name"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reference" className="text-right">
                Reference
              </Label>
              <Input
                id="reference"
                name="reference"
                value={simulatedPayment.reference}
                onChange={handleSimulateInputChange}
                className="col-span-3"
                placeholder="INV-2023-001"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                Amount
              </Label>
              <Input
                id="amount"
                name="amount"
                type="number"
                value={simulatedPayment.amount || ""}
                onChange={handleSimulateInputChange}
                className="col-span-3"
                placeholder="1000.00"
                min="0.01"
                step="0.01"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSimulateModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSimulateSubmit}>Create Payment</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Payment Modal */}
      <ImportPaymentModal
        isOpen={showImportPay}
        onClose={() => setShowImportPay(false)}
      />
    </main>
  );
};

export default AccountsReceivable;
