/**
 * Demo Mode Indicator Component
 * 
 * Shows demo mode status and provides controls for demo functionality.
 */

import React, { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useDemoMode } from '@/hooks/useDemoMode';
import { Activity, Clock, Users, Wifi, WifiOff, History, Trash2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

export function DemoModeIndicator() {
  const {
    isConnected,
    isDemoMode,
    demoStatus,
    getDemoSpeedSeconds,
    eventHistory,
    clearDemoHistory,
    refreshDemoStatus
  } = useDemoMode();

  const [isHistoryOpen, setIsHistoryOpen] = useState(false);

  if (!isDemoMode) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50">
      <Card className="w-80 shadow-lg border-orange-200 bg-orange-50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-orange-600" />
              <CardTitle className="text-sm text-orange-800">Demo Mode</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-green-600" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-600" />
              )}
              <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                Active
              </Badge>
            </div>
          </div>
          <CardDescription className="text-orange-700">
            Simulated payment processing enabled
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-3">
          {/* Status Information */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-3 w-3 text-orange-600" />
              <span className="text-orange-700">
                {getDemoSpeedSeconds()}s delay
              </span>
            </div>
            {demoStatus.clientCount !== undefined && (
              <div className="flex items-center gap-2">
                <Users className="h-3 w-3 text-orange-600" />
                <span className="text-orange-700">
                  {demoStatus.clientCount} connected
                </span>
              </div>
            )}
          </div>

          <Separator className="bg-orange-200" />

          {/* Recent Events */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-orange-800">Recent Events</span>
              <Dialog open={isHistoryOpen} onOpenChange={setIsHistoryOpen}>
                <DialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 px-2 text-orange-700 hover:bg-orange-100">
                    <History className="h-3 w-3 mr-1" />
                    View All
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Demo Event History</DialogTitle>
                    <DialogDescription>
                      Real-time events from the demo payment simulator
                    </DialogDescription>
                  </DialogHeader>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      {eventHistory.length} events
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearDemoHistory}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Clear History
                    </Button>
                  </div>

                  <ScrollArea className="h-96 w-full">
                    <div className="space-y-2">
                      {eventHistory.length === 0 ? (
                        <div className="text-center py-8 text-muted-foreground">
                          No events yet
                        </div>
                      ) : (
                        eventHistory.map((event, index) => (
                          <div
                            key={index}
                            className="p-3 rounded-lg border bg-card text-card-foreground"
                          >
                            <div className="flex items-start justify-between">
                              <div className="space-y-1">
                                <p className="text-sm font-medium">
                                  {event.description}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  {event.type}
                                </p>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {formatDistanceToNow(event.timestamp, { addSuffix: true })}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </DialogContent>
              </Dialog>
            </div>

            {/* Show last 3 events */}
            <div className="space-y-1">
              {eventHistory.slice(-3).map((event, index) => (
                <div
                  key={index}
                  className="text-xs p-2 rounded bg-orange-100 text-orange-800"
                >
                  <div className="font-medium">{event.description}</div>
                  <div className="text-orange-600">
                    {formatDistanceToNow(event.timestamp, { addSuffix: true })}
                  </div>
                </div>
              ))}
              {eventHistory.length === 0 && (
                <div className="text-xs text-orange-600 italic">
                  No events yet
                </div>
              )}
            </div>
          </div>

          <Separator className="bg-orange-200" />

          {/* Controls */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshDemoStatus}
              className="flex-1 text-orange-700 border-orange-300 hover:bg-orange-100"
            >
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
