/**
 * Main Application Component
 *
 * This file serves as the entry point for the client-side application.
 * It sets up the necessary providers and routing for the entire app.
 */

import { Switch, Route } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import NavBar from "@/components/NavBar";
import Footer from "@/components/Footer";
import { DemoModeIndicator } from "@/components/DemoModeIndicator";
import Home from "@/pages/Home";
import AccountsPayable from "@/pages/AccountsPayable";
import AccountsReceivable from "@/pages/AccountsReceivable";
import AdminPage from "@/pages/AdminPage";
import NotFound from "@/pages/not-found";

/**
 * Router Component
 *
 * Handles client-side routing using wouter.
 * Defines the main application layout with consistent navigation and footer.
 * Routes are configured to render different pages based on URL paths.
 *
 * @returns JSX element containing the app's routing structure
 */
function Router() {
  return (
    <div className="min-h-screen flex flex-col">
      <NavBar />
      <div className="flex-1">
        <Switch>
          <Route path="/" component={Home} />
          <Route path="/accounts-payable" component={AccountsPayable} />
          <Route path="/accounts-receivable" component={AccountsReceivable} />
          <Route path="/admin" component={AdminPage} />
          <Route component={NotFound} />
        </Switch>
      </div>
      <Footer />
      <DemoModeIndicator />
    </div>
  );
}

/**
 * Root Application Component
 *
 * Sets up the global provider context for the application:
 * - QueryClientProvider: Manages API data fetching and caching
 * - TooltipProvider: Provides consistent tooltip behavior throughout the app
 * - Toaster: Handles toast notifications
 *
 * @returns JSX element representing the entire application
 */
function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
