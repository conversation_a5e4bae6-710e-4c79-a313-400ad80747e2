/**
 * Demo Payment Gateway Service
 *
 * Simulates blockchain payment processing and webhook responses
 * for demo mode without requiring external services.
 */

import { Request, Response } from 'express';
import { storage } from '../storage';
import { demoEventBus } from './demoEventBus';
import { getDemoSpeed } from '../middleware/demoMode';

interface PendingPayment {
  id: number;
  txHash: string;
  status: 'PENDING' | 'CONFIRMED' | 'FAILED';
  timestamp: Date;
  timeoutId: NodeJS.Timeout;
}

class DemoPaymentGateway {
  private pendingPayments: Map<number, PendingPayment> = new Map();
  private txHashCounter = 1;

  /**
   * Generate a mock blockchain transaction hash
   */
  private generateTxHash(): string {
    const hash = `0x${Math.random().toString(16).substr(2, 64).padStart(64, '0')}`;
    return hash;
  }

  /**
   * Handle payment send request in demo mode
   */
  async handleSendPayment(req: Request, res: Response) {
    try {
      const paymentId = parseInt(req.params.id);

      // Validate payment ID
      if (isNaN(paymentId) || paymentId <= 0) {
        console.error(`Demo payment gateway: Invalid payment ID: ${req.params.id}`);
        return res.status(400).json({ error: "Invalid payment ID" });
      }

      const payment = await storage.getPayment(paymentId);

      if (!payment) {
        return res.status(404).json({ error: "Payment not found" });
      }

      if (!payment.approved) {
        return res.status(400).json({ error: "Payment must be approved before sending" });
      }

      // Generate mock transaction hash
      const txHash = this.generateTxHash();

      // Store as pending payment
      const pendingPayment: PendingPayment = {
        id: paymentId,
        txHash,
        status: 'PENDING',
        timestamp: new Date(),
        timeoutId: setTimeout(() => this.confirmPayment(paymentId), getDemoSpeed())
      };

      this.pendingPayments.set(paymentId, pendingPayment);

      // Emit real-time event
      demoEventBus.emit('payment.sent', {
        paymentId,
        txHash,
        status: 'PENDING',
        timestamp: pendingPayment.timestamp
      });

      // Return immediate response
      res.json({
        status: "pending",
        txHash,
        message: "Payment is being processed on blockchain"
      });

    } catch (error) {
      console.error("Demo payment gateway error:", error);
      res.status(500).json({ error: "Failed to process payment" });
    }
  }

  /**
   * Confirm a pending payment after delay
   */
  private async confirmPayment(paymentId: number) {
    try {
      const pendingPayment = this.pendingPayments.get(paymentId);
      if (!pendingPayment) return;

      // Update payment status in database
      const updatedPayment = await storage.sendPayment(paymentId);

      if (updatedPayment) {
        // Update pending payment status
        pendingPayment.status = 'CONFIRMED';

        // Emit confirmation event
        demoEventBus.emit('payment.confirmed', {
          paymentId,
          txHash: pendingPayment.txHash,
          status: 'CONFIRMED',
          payment: updatedPayment
        });

        console.log(`Demo: Payment ${paymentId} confirmed with tx hash ${pendingPayment.txHash}`);

        // Create corresponding received payment on AR side (demo flow)
        await this.createReceivedPaymentForDemo(updatedPayment);

        // Schedule automatic receipt generation after a short delay
        setTimeout(() => this.generateAutomaticReceipt(paymentId), 2000);
      }

      // Clean up
      this.pendingPayments.delete(paymentId);

    } catch (error) {
      console.error(`Error confirming demo payment ${paymentId}:`, error);

      // Mark as failed and emit event
      const pendingPayment = this.pendingPayments.get(paymentId);
      if (pendingPayment) {
        pendingPayment.status = 'FAILED';
        demoEventBus.emit('payment.failed', {
          paymentId,
          txHash: pendingPayment.txHash,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }
  }

  /**
   * Create a received payment on the AR side for demo flow
   */
  private async createReceivedPaymentForDemo(sentPayment: any) {
    try {
      // Create the received payment data
      const receivedPaymentData = {
        sender: sentPayment.sender,
        amount: sentPayment.amount,
        reference: sentPayment.reference,
        recipient: "Your Company", // Demo recipient
        invoice_id: null // Will be auto-linked if matching invoice exists
      };

      // Create the received payment
      const receivedPayment = await storage.createReceivedPayment(receivedPaymentData);

      if (receivedPayment) {
        console.log(`Demo: Created received payment ${receivedPayment.id} for sent payment ${sentPayment.id}`);

        // Try to auto-link to matching invoice
        const allInvoices = await storage.getAllInvoices();
        const matchingInvoice = allInvoices.find(invoice =>
          invoice.reference === receivedPayment.reference ||
          (invoice.reference.includes('-') && receivedPayment.reference.includes('-') &&
           invoice.reference.split('-').slice(1).join('-') === receivedPayment.reference.split('-').slice(1).join('-'))
        );

        if (matchingInvoice && matchingInvoice.status !== 'Paid') {
          await storage.linkReceivedPaymentToInvoice(receivedPayment.id, matchingInvoice.id);
          await storage.updateInvoiceStatus(matchingInvoice.id, "Paid");

          console.log(`Demo: Auto-linked received payment ${receivedPayment.id} to invoice ${matchingInvoice.id}`);

          // Emit invoice paid event
          demoEventBus.emit('invoice.paid', {
            invoiceId: matchingInvoice.id,
            paymentId: receivedPayment.id,
            amount: receivedPayment.amount,
            timestamp: new Date()
          });
        }

        // Emit received payment event
        demoEventBus.emit('payment.received', {
          paymentId: receivedPayment.id,
          amount: receivedPayment.amount,
          reference: receivedPayment.reference,
          sender: receivedPayment.sender,
          timestamp: new Date()
        });
      }

    } catch (error) {
      console.error(`Error creating received payment for demo:`, error);
    }
  }

  /**
   * Generate automatic receipt for demo mode
   */
  private async generateAutomaticReceipt(paymentId: number) {
    try {
      const payment = await storage.getPayment(paymentId);
      if (!payment || payment.status !== 'Paid') return;

      // Mark payment as having receipt imported
      await storage.markPaymentReceiptImported(paymentId);

      // Emit receipt generated event
      demoEventBus.emit('receipt.generated', {
        paymentId,
        timestamp: new Date()
      });

      console.log(`Demo: Automatic receipt generated for payment ${paymentId}`);

    } catch (error) {
      console.error(`Error generating automatic receipt for payment ${paymentId}:`, error);
    }
  }

  /**
   * Handle incoming payment webhook simulation
   */
  async handleIncomingPayment(req: Request, res: Response) {
    try {
      const { amount, reference, sender } = req.body;

      if (!amount || !reference || !sender) {
        return res.status(400).json({ error: "Missing required fields" });
      }

      // Create received payment record
      const receivedPayment = await storage.markPaymentReceived({
        from: sender,
        amount: parseFloat(amount),
        reference
      });

      if (!receivedPayment) {
        return res.status(404).json({ error: "Reference not found or payment already processed" });
      }

      // Emit incoming payment event
      demoEventBus.emit('payment.received', {
        paymentId: receivedPayment.id,
        amount,
        reference,
        sender,
        timestamp: new Date()
      });

      res.json({
        status: "success",
        receivedPayment
      });

    } catch (error) {
      console.error("Demo incoming payment error:", error);
      res.status(500).json({ error: "Failed to process incoming payment" });
    }
  }

  /**
   * Get all pending payments (for debugging)
   */
  getPendingPayments(): Array<Omit<PendingPayment, 'timeoutId'>> {
    return Array.from(this.pendingPayments.values()).map(({ timeoutId, ...payment }) => payment);
  }

  /**
   * Cancel a pending payment
   */
  cancelPayment(paymentId: number): boolean {
    const pendingPayment = this.pendingPayments.get(paymentId);
    if (pendingPayment) {
      clearTimeout(pendingPayment.timeoutId);
      this.pendingPayments.delete(paymentId);
      return true;
    }
    return false;
  }

  /**
   * Clear all pending payments (for cleanup)
   */
  clearAllPending(): void {
    this.pendingPayments.forEach((pendingPayment, paymentId) => {
      clearTimeout(pendingPayment.timeoutId);
    });
    this.pendingPayments.clear();
  }
}

// Export singleton instance
export const demoPaymentGateway = new DemoPaymentGateway();
