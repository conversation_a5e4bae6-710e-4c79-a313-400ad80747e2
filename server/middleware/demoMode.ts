/**
 * Demo Mode Middleware
 * 
 * Intercepts API calls in demo mode to provide simulated responses
 * without requiring external payment gateways or blockchain services.
 */

import { Request, Response, NextFunction } from 'express';
import { demoPaymentGateway } from '../services/demoPaymentGateway';

/**
 * Middleware to handle demo mode routing
 */
export function demoModeMiddleware(req: Request, res: Response, next: NextFunction) {
  // Only intercept in demo mode
  if (!process.env.DEMO_MODE || process.env.DEMO_MODE !== 'true') {
    return next();
  }

  // Intercept payment send requests
  if (req.method === 'POST' && req.path.match(/^\/api\/payments\/\d+\/send$/)) {
    return demoPaymentGateway.handleSendPayment(req, res);
  }

  // Intercept incoming payment requests
  if (req.method === 'POST' && req.path.match(/^\/api\/payments\/\d+\/incoming$/)) {
    return demoPaymentGateway.handleIncomingPayment(req, res);
  }

  // Continue with normal processing
  next();
}

/**
 * Check if demo mode is enabled
 */
export function isDemoMode(): boolean {
  return process.env.DEMO_MODE === 'true';
}

/**
 * Get demo simulation speed (in milliseconds)
 * Default: 3000ms (3 seconds)
 */
export function getDemoSpeed(): number {
  const speed = parseInt(process.env.DEMO_SPEED || '3000');
  return Math.max(1000, Math.min(10000, speed)); // Clamp between 1-10 seconds
}
