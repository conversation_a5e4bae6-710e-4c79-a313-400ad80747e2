# ProofPay Demo Mode

## Overview

Demo Mode is a self-contained simulation environment that allows sales engineers and prospects to experience the complete AP/AR payment workflow without requiring external blockchain services, payment gateways, or manual file imports.

## Features

### 🔄 **Payment Sending Simulation**
- **Send Payment** action simulates blockchain processing automatically
- Real-time WebSocket events for live status updates
- Configurable simulation speed (1-10 seconds)
- Mock blockchain transaction hashes
- **Manual imports remain available** for invoices, payments, and receipts

### 📥 **Core Workflow Preserved**
- All import buttons remain functional (Import Payment Files, Import Receipt, Import Invoice, Import Payment)
- Demo mode only simulates the blockchain/network parts of payments you send
- Manual control over invoice and payment imports

### 🎯 **Real-time Updates**
- WebSocket-based event broadcasting
- Live status indicators in the UI
- Event history tracking
- No page refreshes required

### 🎮 **Demo Controls**
- Visual demo mode indicator
- Event history viewer
- Simulation speed controls
- Queue status monitoring

## Quick Start

### 1. Enable Demo Mode

Add to your `.env` file:
```bash
DEMO_MODE=true
DEMO_SPEED=3000  # 3 seconds (optional)
```

### 2. Start the Application

```bash
npm run dev
```

### 3. Experience the Flow

1. **Upload a payment file** in Accounts Payable
2. **Approve the payment** with BLS signature
3. **Send the payment** - watch it automatically:
   - Generate blockchain transaction hash
   - Confirm after 3 seconds
   - Auto-import receipt
   - Move to reconciliation column
4. **Generate reconciliation** document

Meanwhile, **incoming payments** will automatically appear in Accounts Receivable and link to open invoices.

## Architecture

### Backend Services

#### `DemoPaymentGateway`
- Intercepts `/api/payments/:id/send` requests
- Generates mock blockchain transaction hashes
- Simulates confirmation delays
- Automatically imports receipts

#### `DemoEventBus`
- WebSocket server for real-time events
- Event history management
- Client connection tracking
- Broadcasting to all connected clients

#### `DemoIncomingPaymentSimulator`
- Generates incoming payments for open invoices
- Configurable simulation queue
- Auto-linking to invoices by reference

#### `DemoModeMiddleware`
- Routes demo requests to simulation services
- Environment-based activation
- API endpoint protection

### Frontend Components

#### `useDemoMode` Hook
- WebSocket connection management
- Real-time event handling
- Query cache invalidation
- Demo status tracking

#### `DemoModeIndicator`
- Visual status indicator
- Event history viewer
- Connection status
- Demo controls

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DEMO_MODE` | `false` | Enable/disable demo mode |
| `DEMO_SPEED` | `3000` | Simulation delay in milliseconds |

### Demo Speed Settings

- **Fast**: 1000ms (1 second) - For quick demonstrations
- **Normal**: 3000ms (3 seconds) - Default, realistic timing
- **Slow**: 5000ms (5 seconds) - For detailed explanations

## API Endpoints

### Demo Status
```
GET /api/demo/status
```
Returns current demo mode configuration and connection info.

### Clear Event History
```
POST /api/demo/clear-history
```
Clears the demo event history.

### Simulator Status
```
GET /api/demo/simulator/status
```
Returns incoming payment simulator queue status.

### Restart Simulator
```
POST /api/demo/simulator/restart
```
Restarts the incoming payment simulator with fresh data.

## WebSocket Events

### Payment Events
- `payment.sent` - Payment submitted to blockchain
- `payment.confirmed` - Payment confirmed on blockchain
- `payment.failed` - Payment processing failed
- `payment.received` - Incoming payment received

### Receipt Events
- `receipt.generated` - Receipt automatically imported

### Invoice Events
- `invoice.paid` - Invoice marked as paid

### Reconciliation Events
- `reconciliation.updated` - Reconciliation status changed

## Demo Workflow

### Accounts Payable Flow
1. **Import Payment File** → Creates payment in "Not Approved" status
2. **Approve Payment** → Moves to "Approved" status with BLS signature
3. **Send Payment** →
   - Immediately shows "Paid" status (optimistic update)
   - Generates mock blockchain transaction hash
   - After 3 seconds: confirms payment
   - After 5 seconds: auto-imports receipt
   - Payment appears in reconciliation column
4. **Generate Reconciliation** → Creates reconciliation document

### Accounts Receivable Flow
1. **Automatic Incoming Payments** → Simulator creates payments for open invoices
2. **Auto-linking** → Payments automatically link to matching invoices
3. **Invoice Status Update** → Invoices marked as "Paid"
4. **Generate Reconciliation** → Creates reconciliation document

## Troubleshooting

### Demo Mode Not Working
- Check `.env` file has `DEMO_MODE=true`
- Restart the server after changing environment variables
- Check browser console for WebSocket connection errors

### WebSocket Connection Issues
- Ensure port 5001 is not blocked
- Check browser developer tools Network tab
- Try refreshing the page

### Events Not Updating UI
- Check WebSocket connection status in demo indicator
- Verify event history is being populated
- Check browser console for errors

## Development

### Adding New Demo Events

1. **Define event type** in `DemoEventBus`
2. **Emit event** from appropriate service
3. **Handle event** in `useDemoMode` hook
4. **Update UI** via query invalidation

### Testing Demo Services

```bash
npm test tests/demo/
```

### Debugging

Enable debug logging:
```bash
DEBUG=demo:* npm run dev
```

## Production Considerations

⚠️ **Important**: Demo mode should **never** be enabled in production environments.

- Demo mode bypasses real payment processing
- Uses mock transaction hashes
- Automatically processes payments without verification
- Intended only for demonstrations and development

Always ensure `DEMO_MODE=false` in production deployments.
